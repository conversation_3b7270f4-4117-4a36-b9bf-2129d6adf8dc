# 三角洲地图扫描监控系统 - WebSocket脚本支持

## 概述

本监控系统现在支持自动检测和提示安装必需的油猴脚本，以确保WebSocket功能正常工作。系统会自动检测脚本状态并提供相应的提示和备用方案。

## 功能特性

### 1. 自动脚本检测
- 系统会自动检测是否安装了必需的油猴脚本
- 支持多种检测方法，确保检测的准确性
- 实时显示脚本状态

### 2. 安装提示
- 如果未检测到脚本，会显示安装提示
- 提供直接链接到脚本安装页面
- 包含详细的安装说明

### 3. 内嵌备用脚本
- 当未安装油猴脚本时，系统会尝试激活内嵌的WebSocket修改功能
- 虽然功能有限，但可以在某些情况下提供基本支持
- 仍然建议安装完整的油猴脚本以获得最佳体验

## 必需的油猴脚本

### 脚本信息
- **名称**: 三角洲地图解密工具
- **版本**: 1.0
- **作者**: MenYu
- **安装链接**: https://greasyfork.org/zh-CN/scripts/544496

### 脚本功能
该脚本的主要功能是将WebSocket连接中的 `wss://` 协议自动转换为 `ws://` 协议，确保监控系统能够正常访问目标服务器。

### 脚本代码
```javascript
// ==UserScript==
// @name         三角洲地图解密工具
// @namespace    https://sjz.exacg.cc/
// @version      1.0
// @description  看猴专用脚本
// <AUTHOR>
// @match        http://*
// @license      zlib
// ==/UserScript==

(function() {
    'use strict';

    // 存储原始 WebSocket 对象
    const OriginalWebSocket = window.WebSocket;

    // 覆盖 WebSocket 构造函数
    window.WebSocket = function(url, protocols) {
        // 替换 wss:// 为 ws://
        const modifiedUrl = url.replace(/^wss:\/\//i, 'ws://');
        console.log(`[WebSocket Override] Original: ${url}, Modified: ${modifiedUrl}`);

        // 使用修改后的 URL 创建 WebSocket 连接
        return new OriginalWebSocket(modifiedUrl, protocols);
    };

})();
```

## 安装步骤

### 1. 安装油猴扩展
首先需要在浏览器中安装油猴（Tampermonkey）扩展：
- Chrome: 从Chrome Web Store安装Tampermonkey
- Firefox: 从Firefox Add-ons安装Tampermonkey
- Edge: 从Microsoft Edge Add-ons安装Tampermonkey

### 2. 安装脚本
1. 点击系统提示中的"安装脚本"链接
2. 或直接访问: https://greasyfork.org/zh-CN/scripts/544496
3. 点击"安装此脚本"按钮
4. 在弹出的Tampermonkey页面中确认安装

### 3. 验证安装
1. 刷新监控系统页面
2. 系统会自动检测脚本状态
3. 如果安装成功，会显示绿色的"✅ 油猴脚本已安装并正常工作"提示

## 使用说明

### 主监控页面
1. 访问 `http://localhost:4000` 打开主监控界面
2. 系统会自动检测脚本状态并显示相应提示
3. 选择左侧的活跃服务器进行预览
4. 右侧iframe会显示服务器内容

### 测试页面
1. 访问 `http://localhost:4000/test` 打开测试页面
2. 点击"测试WSS转换"按钮验证脚本功能
3. 查看测试日志了解详细信息

## 状态指示器

### 脚本状态提示
- **绿色提示**: "✅ 油猴脚本已安装并正常工作" - 油猴脚本正常工作
- **黄色提示**: "⚡ 内嵌脚本已激活" - 使用内嵌备用脚本
- **红色警告**: 显示安装提示 - 需要安装油猴脚本

### 服务器预览状态
- **✅ 脚本已安装**: 在预览头部显示，表示脚本功能正常
- **⚠️ 需要脚本**: 在预览头部显示，建议安装脚本
- **安装提示框**: 在预览区域显示，提供安装链接

## 故障排除

### 脚本未被检测到
1. 确认Tampermonkey扩展已启用
2. 确认脚本已正确安装并启用
3. 刷新页面重新检测
4. 检查浏览器控制台是否有错误信息

### WebSocket连接失败
1. 确认目标服务器支持WebSocket连接
2. 检查网络连接是否正常
3. 尝试使用测试页面验证脚本功能
4. 查看浏览器开发者工具的网络选项卡

### 内嵌脚本限制
内嵌脚本由于浏览器安全限制，可能无法在所有情况下正常工作：
- 跨域限制可能阻止某些连接
- 某些网站的CSP策略可能阻止脚本执行
- 建议安装完整的油猴脚本以获得最佳兼容性

## 技术说明

### 检测机制
系统使用多种方法检测脚本状态：
1. 检查全局变量和WebSocket修改标识
2. 分析WebSocket构造函数是否被修改
3. 检测localStorage中的脚本标记
4. 验证内嵌脚本激活状态

### 安全考虑
- 内嵌脚本仅在未检测到油猴脚本时激活
- 所有WebSocket修改都会在控制台记录
- 不会修改页面的其他功能或数据

## 更新日志

### v1.0 (当前版本)
- 添加自动脚本检测功能
- 实现安装提示和状态显示
- 提供内嵌备用脚本支持
- 添加WebSocket功能测试页面
- 完善用户界面和提示信息
