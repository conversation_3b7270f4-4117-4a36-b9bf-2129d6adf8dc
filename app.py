from flask import Flask, render_template, jsonify
import threading
import websocket
import time
import json
from datetime import datetime
import os
import queue
from collections import defaultdict

app = Flask(__name__)

# 全局变量
server_statuses = {}  # 存储服务器状态
active_servers = []  # 存储活跃服务器列表
detection_results = []  # 存储检测结果
is_detecting = False  # 检测状态标志
stop_event = threading.Event()  # 停止事件
detection_thread = None  # 检测线程

class ServerDetector:
    def __init__(self):
        self.message_counts = defaultdict(int)  # 消息计数器
        self.detection_start_time = None
        
    def load_servers(self):
        """从server.txt加载服务器列表"""
        servers = []
        
        # 优先使用活跃的服务器文件
        for filename in ["active_servers.txt", "websocket_working_servers.txt", 
                        "available_servers.txt", "server.txt"]:
            if os.path.exists(filename):
                with open(filename, "r", encoding="utf-8") as f:
                    servers = [line.strip() for line in f.readlines() if line.strip()]
                print(f"从{filename}中读取到 {len(servers)} 个服务器")
                break
        
        return servers
    
    def test_server_connection(self, server, timeout=10):
        """测试单个服务器连接并计算消息数"""
        message_count = 0
        connected = False
        
        def on_open(ws):
            nonlocal connected
            connected = True
            print(f"成功连接到 {server}")
        
        def on_message(ws, message):
            nonlocal message_count
            message_count += 1
        
        def on_error(ws, error):
            print(f"连接 {server} 时出错: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"连接到 {server} 已关闭")
        
        try:
            # 尝试WS连接
            server_str = str(server).strip()
            if ":" in server_str:
                host = server_str.split(":")[0]
                port = server_str.split(":")[1]
            else:
                host = server_str
                port = "9000"
            
            ws_url = f"ws://{host}:{port}"
            
            ws = websocket.WebSocketApp(
                ws_url,
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )
            
            # 运行连接，限制时间
            ws.run_forever(ping_interval=30, ping_timeout=10)
            
        except Exception as e:
            # 如果WS失败，尝试WSS
            try:
                wss_url = f"wss://{server_str}"
                ws = websocket.WebSocketApp(
                    wss_url,
                    on_open=on_open,
                    on_message=on_message,
                    on_error=on_error,
                    on_close=on_close
                )
                ws.run_forever(sslopt={"cert_reqs": 0}, ping_interval=30, ping_timeout=10)
            except Exception as e2:
                print(f"连接 {server} 失败: {e2}")
        
        return message_count, connected
    
    def detect_active_servers(self, detection_time=10):
        """检测活跃服务器（检测指定时间）"""
        global is_detecting, active_servers, detection_results
        
        is_detecting = True
        self.detection_start_time = datetime.now()
        servers = self.load_servers()
        
        if not servers:
            is_detecting = False
            return []
        
        print(f"开始检测 {len(servers)} 个服务器，检测时间: {detection_time}秒")
        
        # 重置消息计数器
        self.message_counts.clear()
        threads = []
        
        def test_server_with_timeout(server):
            """带超时的服务器测试"""
            start_time = time.time()
            message_count = 0
            
            def on_message_wrapper(ws, message):
                nonlocal message_count
                # 只在检测时间内计数
                if time.time() - start_time < detection_time:
                    message_count += 1
                    self.message_counts[server] = message_count
            
            def on_open_wrapper(ws):
                print(f"连接到 {server}")
            
            def on_error_wrapper(ws, error):
                pass  # 静默处理错误
            
            def on_close_wrapper(ws, close_status_code, close_msg):
                pass  # 静默处理关闭
            
            try:
                server_str = str(server).strip()
                if ":" in server_str:
                    host = server_str.split(":")[0]
                    port = server_str.split(":")[1]
                else:
                    host = server_str
                    port = "9000"
                
                ws_url = f"ws://{host}:{port}"
                
                ws = websocket.WebSocketApp(
                    ws_url,
                    on_open=on_open_wrapper,
                    on_message=on_message_wrapper,
                    on_error=on_error_wrapper,
                    on_close=on_close_wrapper
                )
                
                # 创建一个定时器来关闭连接
                def close_connection():
                    time.sleep(detection_time)
                    try:
                        ws.close()
                    except:
                        pass
                
                timer = threading.Thread(target=close_connection, daemon=True)
                timer.start()
                
                # 运行连接
                ws.run_forever(ping_interval=30, ping_timeout=5)
                
            except Exception as e:
                # 尝试WSS连接
                try:
                    wss_url = f"wss://{server_str}"
                    ws = websocket.WebSocketApp(
                        wss_url,
                        on_open=on_open_wrapper,
                        on_message=on_message_wrapper,
                        on_error=on_error_wrapper,
                        on_close=on_close_wrapper
                    )
                    
                    def close_connection():
                        time.sleep(detection_time)
                        try:
                            ws.close()
                        except:
                            pass
                    
                    timer = threading.Thread(target=close_connection, daemon=True)
                    timer.start()
                    
                    ws.run_forever(sslopt={"cert_reqs": 0}, ping_interval=30, ping_timeout=5)
                except:
                    pass
        
        # 为每个服务器创建线程
        for server in servers:
            if stop_event.is_set():
                break
            thread = threading.Thread(target=test_server_with_timeout, args=(server,), daemon=True)
            threads.append(thread)
            thread.start()
        
        # 等待检测完成
        time.sleep(detection_time + 2)  # 多等待2秒确保所有连接关闭
        
        # 筛选出消息数超过10的服务器
        active_servers = []
        for server, count in self.message_counts.items():
            if count > 10:
                active_servers.append({
                    'server': server,
                    'message_count': count,
                    'detection_time': self.detection_start_time.strftime("%Y-%m-%d %H:%M:%S")
                })
        
        # 按消息数排序
        active_servers.sort(key=lambda x: x['message_count'], reverse=True)
        
        # 保存检测结果
        detection_results.append({
            'timestamp': self.detection_start_time.strftime("%Y-%m-%d %H:%M:%S"),
            'total_servers': len(servers),
            'active_servers': len(active_servers),
            'servers': active_servers.copy()
        })
        
        # 只保留最近10次检测结果
        if len(detection_results) > 10:
            detection_results.pop(0)
        
        print(f"检测完成，发现 {len(active_servers)} 个活跃服务器（消息数>10）")
        is_detecting = False
        
        return active_servers

def auto_detection_loop():
    """自动检测循环：每1分钟检测一次"""
    detector = ServerDetector()
    
    while not stop_event.is_set():
        try:
            print("开始自动检测...")
            detector.detect_active_servers(detection_time=10)
            print("检测完成，等待下次检测...")
            
            # 等待60秒（1分钟）
            for _ in range(60):
                if stop_event.is_set():
                    break
                time.sleep(1)
                
        except Exception as e:
            print(f"自动检测出错: {e}")
            time.sleep(10)  # 出错后等待10秒再重试

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    return jsonify({
        'is_detecting': is_detecting,
        'active_servers_count': len(active_servers),
        'last_detection': detection_results[-1]['timestamp'] if detection_results else None
    })

@app.route('/api/active_servers')
def get_active_servers():
    """获取活跃服务器列表"""
    return jsonify(active_servers)

@app.route('/api/detection_history')
def get_detection_history():
    """获取检测历史"""
    return jsonify(detection_results)

@app.route('/api/start_detection')
def start_detection():
    """手动开始检测"""
    global detection_thread

    if not is_detecting:
        detector = ServerDetector()
        detection_thread = threading.Thread(
            target=detector.detect_active_servers,
            args=(10,),
            daemon=True
        )
        detection_thread.start()
        return jsonify({'status': 'started'})
    else:
        return jsonify({'status': 'already_running'})

@app.route('/test')
def test_page():
    """测试页面 - 用于验证WebSocket功能"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket测试页面</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
            .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
            button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            #log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; }
        </style>
    </head>
    <body>
        <h1>WebSocket功能测试</h1>
        <p>此页面用于测试WebSocket URL转换功能是否正常工作。</p>

        <div class="test-result info">
            <strong>测试说明：</strong><br>
            1. 点击"测试WSS转换"按钮<br>
            2. 如果油猴脚本或内嵌脚本正常工作，wss://地址会被自动转换为ws://<br>
            3. 查看下方日志了解详细信息
        </div>

        <button onclick="testWebSocketConversion()">测试WSS转换</button>
        <button onclick="testDirectWS()">测试直接WS连接</button>
        <button onclick="clearLog()">清空日志</button>

        <h3>测试日志：</h3>
        <div id="log"></div>

        <script>
            function log(message, type = 'info') {
                const logDiv = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
                logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            }

            function testWebSocketConversion() {
                log('开始测试WSS到WS的转换...', 'info');

                try {
                    // 测试wss://地址是否会被转换
                    const testUrl = 'wss://echo.websocket.org';
                    log(`尝试连接: ${testUrl}`, 'info');

                    const ws = new WebSocket(testUrl);

                    ws.onopen = function() {
                        log('WebSocket连接成功！脚本功能正常工作。', 'success');
                        ws.close();
                    };

                    ws.onerror = function(error) {
                        log(`WebSocket连接失败: ${error}`, 'error');
                        log('这可能表示脚本未正确安装或URL转换失败', 'error');
                    };

                    ws.onclose = function() {
                        log('WebSocket连接已关闭', 'info');
                    };

                } catch (error) {
                    log(`测试过程中发生错误: ${error}`, 'error');
                }
            }

            function testDirectWS() {
                log('开始测试直接WS连接...', 'info');

                try {
                    const testUrl = 'ws://echo.websocket.org';
                    log(`尝试连接: ${testUrl}`, 'info');

                    const ws = new WebSocket(testUrl);

                    ws.onopen = function() {
                        log('直接WS连接成功！', 'success');
                        ws.close();
                    };

                    ws.onerror = function(error) {
                        log(`直接WS连接失败: ${error}`, 'error');
                    };

                    ws.onclose = function() {
                        log('直接WS连接已关闭', 'info');
                    };

                } catch (error) {
                    log(`测试过程中发生错误: ${error}`, 'error');
                }
            }

            function clearLog() {
                document.getElementById('log').innerHTML = '';
            }

            // 页面加载时显示WebSocket信息
            window.onload = function() {
                log('页面加载完成', 'info');

                if (window.OriginalWebSocket) {
                    log('检测到WebSocket已被修改（可能是油猴脚本）', 'success');
                } else {
                    log('未检测到WebSocket修改', 'info');
                }

                log(`当前WebSocket构造函数: ${WebSocket.toString().substring(0, 100)}...`, 'info');
            };
        </script>
    </body>
    </html>
    '''

if __name__ == '__main__':
    # 启动自动检测线程
    auto_thread = threading.Thread(target=auto_detection_loop, daemon=True)
    auto_thread.start()
    
    print("三角洲地图扫描网页服务器已启动")
    print("访问 http://localhost:4000 查看监控界面")
    
    try:
        app.run(host='0.0.0.0', port=4000, debug=False)
    except KeyboardInterrupt:
        print("正在关闭服务器...")
        stop_event.set()
